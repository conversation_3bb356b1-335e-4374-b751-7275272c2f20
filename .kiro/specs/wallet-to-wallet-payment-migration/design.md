# Design Document

## Overview

This design document outlines the migration from direct bank transfers to wallet-to-wallet transfers for dues payments, staff payments, and awaiting payments. The solution leverages the existing robust wallet infrastructure (StripeWalletService, walletService) to provide instantaneous payment processing while maintaining all existing functionality, audit trails, and user experience.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Components"
        A[Dues Payment UI]
        B[Staff Payment UI]
        C[Awaiting Payments UI]
    end
    
    subgraph "Backend Services"
        D[Enhanced Dues Service]
        E[Enhanced Staff Service]
        F[Enhanced Awaiting Service]
        G[Wallet Transfer Service]
    end
    
    subgraph "Core Wallet Infrastructure"
        H[StripeWalletService]
        I[WalletService]
        J[Wallet Balance Manager]
    end
    
    subgraph "Database Layer"
        K[Wallet Transactions]
        L[Wallet Balances]
        M[Payment Records]
        N[Audit Logs]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> H
    G --> I
    H --> J
    I --> J
    J --> K
    J --> L
    G --> M
    G --> N
```

### Payment Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Payment UI
    participant PS as Payment Service
    participant WTS as Wallet Transfer Service
    participant WS as Wallet Service
    participant DB as Database
    
    U->>UI: Initiate Payment
    UI->>PS: Process Payment Request
    PS->>WTS: Create Wallet Transfer
    WTS->>WS: Validate Balances
    WS->>DB: Check Sender Balance
    DB-->>WS: Balance Info
    WS-->>WTS: Balance Validated
    WTS->>WS: Execute Transfer
    WS->>DB: Update Balances
    WS->>DB: Create Transactions
    DB-->>WS: Success
    WS-->>WTS: Transfer Complete
    WTS-->>PS: Payment Processed
    PS-->>UI: Success Response
    UI-->>U: Payment Confirmed
```

## Components and Interfaces

### 1. Wallet Transfer Service (New)

**Purpose**: Central service for handling wallet-to-wallet transfers across all payment modules.

**Interface**:
```typescript
interface WalletTransferService {
  transferBetweenWallets(request: WalletTransferRequest): Promise<WalletTransferResult>;
  validateTransferEligibility(request: TransferValidationRequest): Promise<ValidationResult>;
  getTransferHistory(userId: number, filters: TransferHistoryFilters): Promise<TransferHistory[]>;
}

interface WalletTransferRequest {
  fromUserId: number;
  toUserId: number;
  amount: number;
  description: string;
  category: 'dues_payment' | 'staff_payment' | 'awaiting_payment';
  referenceId: string;
  pin: string;
  metadata?: Record<string, any>;
}

interface WalletTransferResult {
  success: boolean;
  transactionId?: string;
  senderNewBalance?: number;
  recipientNewBalance?: number;
  message: string;
  error?: string;
}
```

### 2. Enhanced Dues Service

**Modifications**:
- Replace `payDueWithWallet()` to use new WalletTransferService
- Add balance validation before payment processing
- Maintain existing notification system
- Update payment status tracking

**Key Methods**:
```typescript
interface EnhancedDuesService {
  payDueWithWalletTransfer(request: DuesPaymentRequest): Promise<PaymentResponse>;
  validateDuesPayment(request: DuesPaymentRequest): Promise<ValidationResult>;
  getDuesPaymentHistory(userId: number): Promise<DuesPayment[]>;
}
```

### 3. Enhanced Staff Service

**Modifications**:
- Replace `transferAmountToStaff()` to use wallet transfers instead of bank transfers
- Add bulk payment processing for multiple staff members
- Implement instant payment confirmation
- Maintain existing bank account management for reference

**Key Methods**:
```typescript
interface EnhancedStaffService {
  payStaffWithWalletTransfer(request: StaffPaymentRequest): Promise<PaymentResponse>;
  processBulkStaffPayments(requests: StaffPaymentRequest[]): Promise<BulkPaymentResult>;
  validateStaffPayment(request: StaffPaymentRequest): Promise<ValidationResult>;
}
```

### 4. Enhanced Awaiting Payments Service

**Modifications**:
- Create new service for processing awaiting payments via wallet transfers
- Add batch processing capabilities
- Implement approval workflow integration
- Maintain existing payment tracking

**Key Methods**:
```typescript
interface EnhancedAwaitingPaymentsService {
  processAwaitingPaymentWithWallet(request: AwaitingPaymentRequest): Promise<PaymentResponse>;
  processBulkAwaitingPayments(requests: AwaitingPaymentRequest[]): Promise<BulkPaymentResult>;
  getAwaitingPaymentStatus(paymentId: string): Promise<PaymentStatus>;
}
```

### 5. Frontend Component Updates

**Modifications Required**:
- Update payment confirmation flows to show instant success
- Add wallet balance checks before payment initiation
- Modify payment method selection to prioritize wallet transfers
- Update transaction history displays to distinguish wallet transfers
- Add "Add Money" quick links for insufficient balance scenarios

## Data Models

### 1. Enhanced Wallet Transaction Model

```typescript
interface EnhancedWalletTransaction {
  id: number;
  user_id: number;
  amount: number;
  transaction_type: 'dues_payment' | 'staff_payment' | 'awaiting_payment' | 'wallet_transfer';
  category: 'dues' | 'staff' | 'awaiting' | 'general';
  reference_id: string;
  related_user_id?: number; // For transfers
  description: string;
  status_id: number; // 1=completed, 2=pending, 3=failed
  metadata: {
    payment_category: string;
    original_due_id?: string;
    staff_member_id?: string;
    awaiting_payment_id?: string;
    transfer_type: 'send' | 'receive';
    counterpart_user_id?: number;
    counterpart_name?: string;
  };
  created_at: Date;
  updated_at: Date;
}
```

### 2. Payment Tracking Model

```typescript
interface PaymentTrackingRecord {
  id: number;
  payment_type: 'dues' | 'staff' | 'awaiting';
  original_payment_id: string;
  wallet_transaction_id: number;
  sender_user_id: number;
  recipient_user_id: number;
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  processed_at: Date;
  metadata: Record<string, any>;
}
```

## Error Handling

### 1. Validation Errors

**Insufficient Balance**:
- Check available balance before processing
- Provide clear error messages with current balance
- Suggest "Add Money" action with direct links

**Invalid Recipients**:
- Validate recipient user exists and has active wallet
- Check recipient eligibility for payment type
- Provide meaningful error messages

**PIN Validation**:
- Maintain existing PIN verification system
- Implement rate limiting for failed attempts
- Clear error messaging for PIN failures

### 2. Transaction Failures

**Rollback Strategy**:
- Use database transactions for atomic operations
- Implement compensation patterns for partial failures
- Maintain audit trail of failed transactions

**Retry Logic**:
- Implement exponential backoff for transient failures
- Maximum retry attempts with circuit breaker pattern
- Clear failure notifications to users

### 3. System Failures

**Graceful Degradation**:
- Fallback to pending status for system unavailability
- Queue failed transactions for retry
- Maintain user notifications for delayed processing

## Testing Strategy

### 1. Unit Testing

**Service Layer Tests**:
- WalletTransferService functionality
- Balance validation logic
- PIN verification
- Transaction creation and rollback

**Integration Tests**:
- End-to-end payment flows
- Database transaction integrity
- Notification system integration
- Error handling scenarios

### 2. Performance Testing

**Load Testing**:
- Concurrent payment processing
- Bulk payment operations
- Database performance under load
- Wallet balance consistency

**Stress Testing**:
- High-volume transaction scenarios
- System resource utilization
- Database connection pooling
- Memory usage patterns

### 3. Security Testing

**Authentication & Authorization**:
- PIN verification security
- User permission validation
- Cross-user transaction prevention
- Session management

**Data Protection**:
- Sensitive data encryption
- Audit trail integrity
- Transaction immutability
- PCI compliance maintenance

## Migration Strategy

### 1. Backward Compatibility

**Existing Data**:
- Maintain existing bank transfer records
- Clear distinction between historical and new transactions
- Preserve existing audit trails
- Support mixed transaction history views

**API Compatibility**:
- Maintain existing API endpoints
- Add new wallet-specific endpoints
- Gradual migration of frontend components
- Feature flag support for rollback

### 2. Deployment Approach

**Phase 1: Infrastructure**:
- Deploy WalletTransferService
- Update database schemas
- Implement monitoring and logging

**Phase 2: Backend Services**:
- Update dues service
- Update staff service
- Update awaiting payments service
- Comprehensive testing

**Phase 3: Frontend Updates**:
- Update payment UIs
- Update transaction displays
- User acceptance testing
- Gradual rollout

### 3. Monitoring and Observability

**Key Metrics**:
- Payment success rates
- Transaction processing times
- Wallet balance accuracy
- User adoption rates

**Alerting**:
- Failed transaction thresholds
- Balance inconsistencies
- System performance degradation
- Security anomalies

**Logging**:
- Comprehensive transaction logging
- User action tracking
- Error condition logging
- Performance metrics collection