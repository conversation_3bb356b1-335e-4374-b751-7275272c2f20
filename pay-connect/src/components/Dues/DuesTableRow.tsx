import React from 'react';
import { Due } from '../../services/dues';
import { Building2, Send, History, Trash2, CreditCard, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DuesTableRowProps {
  due: Due;
  onDelete: (id: string) => void;
  onPayNow: (due: Due) => void;
  onAddBankAccount: (due: Due) => void;
  onBankTransfer: (due: Due) => void;
  isSelected?: boolean;
  onSelect?: (checked: boolean) => void;
}

const DuesTableRow: React.FC<DuesTableRowProps> = ({ 
  due, 
  onDelete, 
  onPayNow, 
  onAddBankAccount,
  onBankTransfer,
  isSelected = false, 
  onSelect 
}) => {
  const navigate = useNavigate();

  const getStatusColor = (status: Due['status']) => {
    switch (status) {
      case 'Paid':
        return 'text-green-600';
      case 'Pending':
        return 'text-yellow-600';
      case 'Overdue':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const isOverdue = due.status === 'Overdue';
  const canPay = due.status === 'Pending' || due.status === 'Overdue';
  const canSelect = due.status === 'Pending' || due.status === 'Overdue';

  const handleViewDetails = () => {
    navigate(`/dues/${due.user_payconnect_id}`);
  };

  const handleAddBankAccount = () => {
    onAddBankAccount(due);
  };

  const handleBankTransfer = () => {
    onBankTransfer(due);
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSelect) {
      onSelect(e.target.checked);
    }
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        {canSelect ? (
          <input 
            type="checkbox" 
            className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
            checked={isSelected}
            onChange={handleSelectChange}
          />
        ) : (
          <div className="w-4 h-4"></div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <img className="h-10 w-10 rounded-full" src={due.avatarUrl || due.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(due.firstname)}`} alt="" />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              <button 
                onClick={handleViewDetails}
                className="hover:text-blue-600 flex items-center"
              >
                {due.full_name || due.firstname}
                <ExternalLink size={14} className="ml-1 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.email}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.contact}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.game_title}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.season_name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${due.amount}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {new Date(due.end_date,).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <span className={getStatusColor(due.status)}>
          {due.status}
          {isOverdue}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-start space-x-2">
          <button
            onClick={handleAddBankAccount}
            className="text-blue-500 hover:text-blue-500 ml-3 mt-1 p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-500 transition"
            title="Add Bank Account"
          >
            <Building2 size={18} />
          </button>
          <button
            onClick={handleBankTransfer}
            className="text-green-500 hover:text-green-500 ml-3 mt-1 p-2 rounded-full bg-green-50 hover:bg-green-100 text-green-500 transition"
            title="Bank Transfer"
          >
            <Send size={18} />
          </button>
          <button
            onClick={handleViewDetails}
            className="text-purple-500 hover:text-purple-500 ml-3 mt-1 p-2 rounded-full bg-purple-50 hover:bg-purple-100 text-purple-500 transition"
            title="View Transaction History"
          >
            <History size={18} />
          </button>
          {/* {canPay && (
            <button 
              onClick={() => onPayNow(due)}
              className="text-orange-500 hover:text-orange-500 ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100 text-orange-500 transition"
              title="Pay Now"
            >
              <CreditCard size={18} />
            </button>
          )} */}
          <button 
            onClick={() => onDelete(due.id)}
            className="text-red-500 hover:text-red-500 ml-3 mt-1 p-2 rounded-full bg-red-50 hover:bg-red-100 text-red-500 transition"
            title="Delete"
          >
            <Trash2 size={18} />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default DuesTableRow; 