import { Request, Response } from 'express';
import {
  getCurrentUserStaff,
  getUserDetails,
  getUserDepartment,
  getUserManager,
  getUserTeamMembers,
  getUserProjects,
  getUserPermissions,
  getComprehensiveUserInfo,
  importStaffUsers,
  ImportableUser,
  getStaffTransactionHistory,

  transferAmountToStaff
} from '../services/staffService';
import { executeQuery } from '../utils/database';
import { sendSuccess, sendError, sendUnauthorized, sendUserSuccess, sendUserError } from '../utils/response';
import logger from '../utils/logger';

/**
 * Get current user's staff members
 */
export const getUserStaff = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const team_connect_user_id = req.team_connect_user_id;
    if (!userId || !team_connect_user_id) {
      return sendUnauthorized(res, 'Authentication required');
    }
    console.log(userId, team_connect_user_id, req);

    const staff = await getCurrentUserStaff(parseInt(userId), parseInt(team_connect_user_id));
    sendSuccess(res, staff, 'Staff members retrieved successfully');
  } catch (error) {
    logger.error('Error getting user staff', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      team_connect_user_id: req.team_connect_user_id
    });
    sendError(res, 'Failed to retrieve staff members', 500);
  }
};

/**
 * Get user details from secondary database
 */
export const getUserDetailsInfo = async (req: Request & { userId?: string; team?: string }, res: Response) => {
  try {
   const userId = req.query.id as string;
   

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const userDetails = await getUserDetails(parseInt(userId));

    if (!userDetails) {
      return sendError(res, 'User details not found', 404);
    }

    sendSuccess(res, userDetails, 'User details retrieved successfully');
  } catch (error) {
    logger.error('Error getting user details', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.master_parent_user_id
    });
    sendError(res, 'Failed to retrieve user details', 500);
  }
};

/**
 * Get user's department information
 */
export const getUserDepartmentInfo = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const department = await getUserDepartment(parseInt(userId));
    sendSuccess(res, department, 'Department information retrieved successfully');
  } catch (error) {
    logger.error('Error getting user department', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve department information', 500);
  }
};

/**
 * Get user's manager information
 */
export const getUserManagerInfo = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const manager = await getUserManager(parseInt(userId));
    sendSuccess(res, manager, 'Manager information retrieved successfully');
  } catch (error) {
    logger.error('Error getting user manager', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve manager information', 500);
  }
};

/**
 * Get user's team members
 */
export const getUserTeam = async (req: Request & { userId?: string } & { team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.team_connect_user_id;
    
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const teamMembers = await getUserTeamMembers(parseInt(userId));
    sendSuccess(res, teamMembers, 'Team members retrieved successfully');
  } catch (error) {
    logger.error('Error getting user team', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve team members', 500);
  }
};

/**
 * Get user's projects
 */
export const getUserProjectsInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const projects = await getUserProjects(parseInt(userId));
    sendSuccess(res, projects, 'Projects retrieved successfully');
  } catch (error) {
    logger.error('Error getting user projects', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve projects', 500);
  }
};

/**
 * Get user's permissions
 */
export const getUserPermissionsInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const permissions = await getUserPermissions(parseInt(userId));
    sendSuccess(res, permissions, 'Permissions retrieved successfully');
  } catch (error) {
    logger.error('Error getting user permissions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve permissions', 500);
  }
};

/**
 * Get comprehensive user information
 */
export const getComprehensiveUserData = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const comprehensiveInfo = await getComprehensiveUserInfo(
      parseInt(userId),
      parentUserId ? parseInt(parentUserId) : undefined
    );
    sendSuccess(res, comprehensiveInfo, 'Comprehensive user information retrieved successfully');
  } catch (error) {
    logger.error('Error getting comprehensive user info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve comprehensive user information', 500);
  }
};

/**
 * Import multiple users as staff members
 */
export const importUsers = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const team_connect_user_id = req.team_connect_user_id;
    const { users, defaultRole } = req.body;

    if (!userId || !team_connect_user_id) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Validate request body
    if (!users || !Array.isArray(users) || users.length === 0) {
      return sendError(res, 'Users array is required and cannot be empty', 400);
    }

    // Validate each user object
    for (const user of users) {
      if (!user.id || !user.name || !user.email) {
        return sendError(res, 'Each user must have id, name, and email', 400);
      }
    }

    // Validate default role
    const validRoles = ['Staff', 'Referee'];
    if (defaultRole && !validRoles.includes(defaultRole)) {
      return sendError(res, 'Invalid default role. Must be one of: Office Staff, Referee, Other Staff', 400);
    }
    console.log(users, defaultRole);

    // Import users
    const result = await importStaffUsers(
      users as ImportableUser[],
      parseInt(team_connect_user_id),
      defaultRole
    );

    if (result.success) {
      logger.info('Staff import completed successfully', {
        userId,
        team_connect_user_id,
        imported: result.imported,
        failed: result.failed
      });

      sendSuccess(res, {
        imported: result.imported,
        failed: result.failed,
        message: result.message,
        errors: result.errors
      }, 'Staff import completed');
    } else {
      logger.warn('Staff import completed with errors', {
        userId,
        team_connect_user_id,
        imported: result.imported,
        failed: result.failed,
        errors: result.errors
      });

      sendError(res, result.message, 400, result.errors?.join('; '));
    }

  } catch (error) {
    logger.error('Error importing staff users', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.master_parent_user_id
    });
    sendError(res, 'Failed to import staff users', 500);
  }
};

/**
 * Get staff member's transaction history with pagination and filtering
 */
export const getStaffTransactions = async (req: Request, res: Response) => {
  try {
    const userId = req.query.userId as string;
    const {
      limit = '20',
      offset = '0',
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    logger.info('Staff transactions request received', {
      userId,
      userIdType: typeof userId,
      allQueryParams: req.query,
      limit,
      offset,
      search,
      type,
      status,
      dateFrom,
      dateTo
    });

    if (!userId) {
      logger.error('Missing userId parameter');
      return sendError(res, 'userId parameter required', 400);
    }

    if (userId === 'undefined' || userId === 'null') {
      logger.error('Invalid userId parameter', { userId });
      return sendError(res, 'Invalid userId parameter', 400);
    }

    const result = await getStaffTransactionHistory(parseInt(userId), {
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      search: search as string,
      type: type as string,
      status: status as string,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string
    });

    sendSuccess(res, {
      transactions: result.transactions,
      pagination: {
        total: result.total,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: result.hasMore,
        currentPage: Math.floor(parseInt(offset as string) / parseInt(limit as string)) + 1,
        totalPages: Math.ceil(result.total / parseInt(limit as string))
      },
      filters: {
        search: search as string,
        type: type as string,
        status: status as string,
        dateFrom: dateFrom as string,
        dateTo: dateTo as string
      }
    }, 'Staff transactions retrieved successfully');

  } catch (error) {
    logger.error('Error fetching staff transactions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.query.userId
    });
    sendError(res, 'Failed to retrieve staff transactions', 500);
  }
};











