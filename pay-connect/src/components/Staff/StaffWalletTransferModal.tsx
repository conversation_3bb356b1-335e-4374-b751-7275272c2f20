import React, { useState, useEffect } from 'react';
import { X, DollarSign, Lock, Eye, EyeOff, ArrowRight, CheckCircle, AlertCircle, User, Send, Wallet } from 'lucide-react';
import { StaffMember, transferToStaffWallet } from '../../services/staff';
import { getWallet } from '../../services/wallet';
import PaymentLoader from '../common/PaymentLoader';

interface StaffWalletTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  staffId: string;
  staffName: string;
  onSuccess: () => void;
}

type TransferStep = 'details' | 'confirmation' | 'processing' | 'success' | 'error';

const StaffWalletTransferModal: React.FC<StaffWalletTransferModalProps> = ({
  isOpen,
  onClose,
  staffId,
  staffName,
  onSuccess
}) => {
  const [step, setStep] = useState<TransferStep>('details');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [transactionId, setTransactionId] = useState<number | null>(null);
  const [currentBalance, setCurrentBalance] = useState(0);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('details');
      setAmount('');
      setDescription('');
      setPin('');
      setShowPin(false);
      setError(null);
      setLoading(false);
      setTransactionId(null);
      fetchWalletBalance();
    }
  }, [isOpen]);

  const fetchWalletBalance = async () => {
    try {
      const wallet = await getWallet();
      setCurrentBalance(wallet.balance);
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      setError('Failed to fetch wallet balance');
    }
  };

  const handleClose = () => {
    if (step === 'processing') return; // Prevent closing during processing
    onClose();
  };

  const validateForm = () => {
    if (!staffId) {
      setError('No staff member selected');
      return false;
    }
    if (!amount || parseFloat(amount) <= 0) {
      setError('Please enter a valid amount');
      return false;
    }
    if (parseFloat(amount) > currentBalance) {
      setError('Insufficient wallet balance');
      return false;
    }
    if (!pin || pin.length < 4) {
      setError('PIN must be at least 4 digits');
      return false;
    }
    return true;
  };

  const handleContinue = () => {
    setError(null);
    if (validateForm()) {
      setStep('confirmation');
    }
  };

  const handleConfirmTransfer = async () => {
    if (!staffId || !amount || !pin) return;

    setStep('processing');
    setLoading(true);
    setError(null);

    try {
      const transferAmount = parseFloat(amount);

      const result = await transferToStaffWallet(
        staffId,
        transferAmount,
        description || `Payment to ${staffName}`,
        pin
      );

      if (result.success) {
        setTransactionId(result.transactionId || 0);
        setStep('success');
        onSuccess();
      } else {
        setError(result.message);
        setStep('error');
      }
    } catch (err) {
      console.error('Transfer error:', err);
      setError('Transfer failed. Please try again.');
      setStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToDetails = () => {
    setStep('details');
    setError(null);
  };

  const handleTryAgain = () => {
    setStep('details');
    setError(null);
    setPin(''); // Clear PIN for security
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {step === 'details' && 'Transfer to Staff'}
              {step === 'confirmation' && 'Confirm Transfer'}
              {step === 'processing' && 'Processing Transfer'}
              {step === 'success' && 'Transfer Successful'}
              {step === 'error' && 'Transfer Failed'}
            </h2>
            <p className={`text-sm ${currentBalance === 0 ? 'text-red-500' : currentBalance > 0 ? 'text-green-600' : 'text-gray-500'}`}>
              Available balance: ${currentBalance.toFixed(2)}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={step === 'processing'}
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'details' && (
            <div className="space-y-6">
              {/* Staff Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{staffName}</p>
                    <p className="text-sm text-gray-600">Staff Member</p>
                  </div>
                </div>
              </div>

              {/* Amount Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Transfer Amount
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.00"
                    min="1"
                    max="10000"
                    step="0.01"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Description Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="e.g., Salary payment, bonus, etc."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              {/* Continue Button */}
              <button
                onClick={handleContinue}
                disabled={!amount || !description.trim()}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <span>Continue</span>
                <ArrowRight className="h-4 w-4" />
              </button>
            </div>
          )}

          {step === 'confirmation' && (
            <div className="space-y-6">
              {/* Transfer Summary */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h3 className="font-medium text-gray-900">Transfer Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">To:</span>
                    <span className="font-medium">{staffName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span className="font-medium">${parseFloat(amount || '0').toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Description:</span>
                    <span className="font-medium">{description}</span>
                  </div>
                </div>
              </div>

              {/* PIN Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter your PIN to confirm
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type={showPin ? 'text' : 'password'}
                    value={pin}
                    onChange={(e) => setPin(e.target.value)}
                    placeholder="Enter PIN"
                    maxLength={4}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPin ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              {/* Confirmation Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleBackToDetails}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300"
                >
                  Back
                </button>
                <button
                  onClick={handleConfirmTransfer}
                  disabled={!pin || pin.length < 4}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Confirm Transfer
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <PaymentLoader
                type="processing"
                message="Processing your transfer..."
                size="large"
                showQuotes={true}
              />
            </div>
          )}

          {step === 'success' && (
            <div className="text-center py-8 space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Transfer Successful!</h3>
                <p className="text-gray-600 mt-1">
                  ${parseFloat(amount || '0').toFixed(2)} has been transferred to {staff?.name}
                </p>
                {transactionId && (
                  <p className="text-sm text-gray-500 mt-2">
                    Transaction ID: {transactionId}
                  </p>
                )}
              </div>
              <button
                onClick={handleClose}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700"
              >
                Done
              </button>
            </div>
          )}

          {step === 'error' && (
            <div className="text-center py-8 space-y-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Transfer Failed</h3>
                <p className="text-gray-600 mt-1">{error}</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleClose}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleTryAgain}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StaffWalletTransferModal;
