# Implementation Plan

- [x] 1. Remove Plaid Dependencies from Dues Service
  - Modify `payDueWithWallet` function in duesService.ts to use direct StripeWalletService.transferBetweenWallets instead of Plaid validation
  - Remove bank account validation steps and use direct wallet-to-wallet transfers
  - Update transaction metadata to reflect wallet transfer instead of bank transfer
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 2. Remove Plaid Dependencies from Staff Service  
  - Update `transferAmountToStaff` function in staffService.ts to bypass bank account checks
  - Replace Plaid bank transfer logic with direct StripeWalletService.transferBetweenWallets calls
  - Remove bank account validation and use wallet transfers for staff payments
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 3. Remove Plaid Dependencies from Awaiting Payments
  - Update awaiting payments processing in duesService.ts to use wallet transfers
  - Remove bank account validation steps from awaiting payment flows
  - Implement direct wallet-to-wallet transfers for approved awaiting payments
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 4. Update Transaction Status to Instant Success
  - Modify all payment services to set transaction status_id to '1' (completed) immediately instead of '2' (pending)
  - Remove pending status logic that was waiting for bank transfer confirmation
  - Update payment response messages to indicate instant completion
  - _Requirements: 1.2, 2.2, 3.2, 5.3_

- [x] 5. Update Frontend Payment Confirmations
  - Modify dues payment UI components to show "Payment Completed" instead of "Payment Pending"
  - Update staff payment interface to display instant success messages
  - Change awaiting payments UI to show immediate completion status
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 6. Clean Up Bank Account References
  - Remove unused bank account validation code from payment flows
  - Keep bank account management UI for reference but remove from payment processing
  - Update payment metadata to remove bank account references
  - _Requirements: 4.1, 4.3_

- [ ] 7. Update Error Handling for Direct Transfers
  - Simplify error handling to focus on wallet balance and PIN validation only
  - Remove bank account related error messages and validations
  - Update insufficient balance messages to suggest wallet top-up instead of bank account issues
  - _Requirements: 6.1, 6.2, 6.4_

- [ ] 8. Test Direct Wallet Transfer Flows
  - Test dues payments using direct wallet transfers without Plaid validation
  - Test staff payments with immediate wallet-to-wallet processing
  - Test awaiting payments with instant wallet transfer completion
  - Verify all payments complete instantly with correct balance updates
  - _Requirements: 1.1, 2.1, 3.1, 5.1_